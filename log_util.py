import logging
import os
from datetime import datetime

def setup_logging(log_file='user_recommendation.log'):
    """
    Setup logging configuration to write to both console and file
    """
    # Create logs directory if it doesn't exist
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # Full path to log file
    log_path = os.path.join(log_dir, log_file)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, mode='a', encoding='utf-8'),  # Append to file
            logging.StreamHandler()  # Also output to console
        ]
    )

    # Get logger
    logger = logging.getLogger(__name__)

    # Log session start
    logger.info("="*60)
    logger.info(f"🚀 User Recommendation System - Session Started")
    logger.info(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📁 Log file: {log_path}")
    logger.info("="*60)

    return logger