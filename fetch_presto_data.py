import pandas as pd
from prestodb import dbapi

def fetch_last_day_features_df(s_sql):
    conn = dbapi.connect(
        host='*********',
        port=8082,
        user='ta',
        catalog='hive',
        schema='ws_default_dev',
        http_scheme='http',
    )
    cur = conn.cursor()
    sql_str = s_sql
    
    cur.execute(sql_str)
    rows = cur.fetchall()
    columns = [desc[0] for desc in cur.description]
    conn.close()
    return pd.DataFrame(rows, columns=columns)

sql_machine_value = '''
SELECT "machine_id", "distinct_user_num", "avg_ggr", "popularity_score", "machine_value", "machine_value_rank", "up_date" FROM "hive"."ws_default_product"."lucky_legends_machine_value" 

'''

sql_user_machine_selected_bundle = '''
SELECT "account_id", "bet_amount_totals", "bundle_id", "up_date" FROM "hive"."ws_default_product"."lucky_legends_user_machine" 
'''