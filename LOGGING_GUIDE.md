# 📝 User Recommendation System - Logging Guide

## Overview

The User Recommendation System now includes comprehensive logging functionality that captures all execution details to log files while maintaining console output for real-time monitoring.

## 🔧 Logging Features

### ✅ Key Features
- **Dual Output**: Logs to both console and file simultaneously
- **Automatic Directory Creation**: Creates `logs/` directory if it doesn't exist
- **Append Mode**: Preserves previous log entries across multiple runs
- **UTF-8 Encoding**: Supports emoji and international characters
- **Timestamping**: Precise timestamps with milliseconds
- **Multiple Log Levels**: INFO, WARNING, ERROR with full exception tracebacks
- **Session Tracking**: Clear session start/end markers
- **Performance Monitoring**: Execution time tracking for operations
- **Progress Tracking**: Detailed batch processing progress

## 📁 Log File Structure

```
project_root/
├── logs/
│   ├── user_recommendation.log    # Default log file
│   ├── test_logging_demo.log      # Test log file
│   └── custom_log_name.log        # Custom log files
├── user_recommendation.py
└── other_files...
```

## 📊 Log Format

```
YYYY-MM-DD HH:MM:SS,mmm - LEVEL - MESSAGE
```

### Example Log Entries:
```
2025-07-31 13:10:09,050 - INFO - 🚀 User Recommendation System - Session Started
2025-07-31 13:10:10,416 - INFO - ✅ Generated recommendations for 1000 users
2025-07-31 13:10:11,089 - WARNING - ⚠️  API response took longer than expected
2025-07-31 13:10:12,123 - ERROR - ❌ Error posting to API1: Connection timeout
2025-07-31 13:10:13,456 - INFO - 🏁 Session completed successfully
```

## 🚀 Usage

### Basic Usage
```python
from user_recommendation import setup_logging, run_full_recommendation_pipeline

# Setup logging (creates logs/user_recommendation.log)
logger = setup_logging()

# Run the pipeline (all operations will be logged)
run_full_recommendation_pipeline()
```

### Custom Log File
```python
from user_recommendation import setup_logging

# Setup logging with custom file name
logger = setup_logging('my_custom_log.log')
```

### Individual Functions
```python
from user_recommendation import post_data_to_api_batch, post_data_to_api2

# All functions automatically use logging once setup_logging() is called
post_data_to_api_batch()  # Logs all batch processing details
post_data_to_api2()       # Logs machine value fetching and API calls
```

## 📋 What Gets Logged

### Session Information
- Session start/end timestamps
- Total execution time
- Log file location
- System status

### Data Processing
- Database connection status
- Number of records fetched
- Data preprocessing progress
- CF recommendation generation

### API Operations
- API endpoint URLs
- Request payload sizes
- Response status codes
- Response content
- Batch processing progress

### Error Handling
- Exception messages
- Full error tracebacks
- Recovery attempts
- Failure summaries

### Performance Metrics
- Execution time for each operation
- Batch processing statistics
- Success/failure counts
- Overall pipeline performance

## 🔍 Log Levels

### INFO Level
- Normal operations
- Progress updates
- Success messages
- Performance metrics

### WARNING Level
- Non-critical issues
- Slow operations
- Partial failures
- Recovery actions

### ERROR Level
- Critical errors
- API failures
- Database connection issues
- Exception details

## 📊 Sample Log Output

```
2025-07-31 13:10:09,050 - INFO - ============================================================
2025-07-31 13:10:09,050 - INFO - 🚀 User Recommendation System - Session Started
2025-07-31 13:10:09,050 - INFO - 📅 Timestamp: 2025-07-31 13:10:09
2025-07-31 13:10:09,051 - INFO - 📁 Log file: logs/user_recommendation.log
2025-07-31 13:10:09,051 - INFO - ============================================================
2025-07-31 13:10:09,052 - INFO - 🔄 Generating personalized recommendations using CF...
2025-07-31 13:15:30,123 - INFO - ✅ Generated recommendations for 215541 users
2025-07-31 13:15:30,124 - INFO - 📦 Starting batch processing: 215541 rows in 216 batches
2025-07-31 13:15:30,125 - INFO - 🔄 Processing batch 1/216
2025-07-31 13:15:32,456 - INFO - ✅ Batch 1: Success - 1000 rows
2025-07-31 13:15:32,457 - INFO - 🔄 Processing batch 2/216
...
2025-07-31 13:25:45,789 - INFO - 📊 Batch processing completed:
2025-07-31 13:25:45,789 - INFO -    ✅ Successful batches: 216
2025-07-31 13:25:45,789 - INFO -    ❌ Failed batches: 0
2025-07-31 13:25:45,789 - INFO -    📈 Total rows processed: 215541
2025-07-31 13:25:45,790 - INFO - ⏱️  API1 execution time: 615.67 seconds
2025-07-31 13:25:45,791 - INFO - 🎯 Step 2: Common Recommendations (API2)
2025-07-31 13:25:45,792 - INFO - 🔄 Fetching machine value data from database...
2025-07-31 13:25:46,123 - INFO - ✅ Fetched 30 machines from database
2025-07-31 13:25:46,124 - INFO - 🏆 Top 10 machines by rank:
2025-07-31 13:25:46,124 - INFO -    1. lottery (rank: 1, value: 10.46)
2025-07-31 13:25:46,125 - INFO - ✅ API2 Response Status: 200
2025-07-31 13:25:46,126 - INFO - ⏱️  API2 execution time: 0.33 seconds
2025-07-31 13:25:46,127 - INFO - ✅ Full Recommendation Pipeline Completed!
2025-07-31 13:25:46,127 - INFO - ⏱️  Total execution time: 616.00 seconds
2025-07-31 13:25:46,128 - INFO - 🎉 Pipeline completed successfully!
2025-07-31 13:25:46,129 - INFO - ============================================================
2025-07-31 13:25:46,129 - INFO - 🏁 Session completed successfully
2025-07-31 13:25:46,129 - INFO - ============================================================
```

## 🛠️ Configuration

The logging system is automatically configured when you call `setup_logging()`. Default settings:

- **Log Level**: INFO (captures INFO, WARNING, ERROR)
- **Log Directory**: `logs/`
- **Default File**: `user_recommendation.log`
- **Format**: Timestamp - Level - Message
- **Encoding**: UTF-8
- **Mode**: Append (preserves previous logs)

## 🔧 Troubleshooting

### Log File Not Created
- Check write permissions in the project directory
- Ensure the script has permission to create directories

### Large Log Files
- Log files grow with each execution
- Consider implementing log rotation for production use
- Monitor disk space usage

### Missing Log Entries
- Ensure `setup_logging()` is called before other functions
- Check that the logging level is appropriate

## 🎯 Best Practices

1. **Always call `setup_logging()` first** before running any operations
2. **Use descriptive log file names** for different operations
3. **Monitor log file sizes** in production environments
4. **Review logs regularly** for performance optimization
5. **Archive old logs** to manage disk space

## 📈 Benefits

- **Debugging**: Detailed error information and tracebacks
- **Monitoring**: Real-time progress tracking
- **Auditing**: Complete execution history
- **Performance**: Execution time analysis
- **Troubleshooting**: Comprehensive error context
- **Compliance**: Detailed operation records
