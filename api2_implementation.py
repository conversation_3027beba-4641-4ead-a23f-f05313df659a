import pandas as pd
import requests
import json

def post_data_to_api2(csv_file='example2.csv'):
    """
    Implementation for API2: update_common endpoint
    Reads machine data from CSV, orders by rank, and sends top 10 machines to API2
    
    Args:
        csv_file (str): Path to the CSV file containing machine data
    
    Returns:
        requests.Response: API response object
    """
    # Read data from CSV
    df = pd.read_csv(csv_file)
    
    # Sort by machine_value_rank (ascending order, rank 1 is best)
    df_sorted = df.sort_values('machine_value_rank')
    
    # Take top 10 machines
    top_10_machines = df_sorted.head(10)
    
    # Create the machine position mapping
    machine_positions = {}
    for i, (_, row) in enumerate(top_10_machines.iterrows(), 1):
        machine_positions[f"machine_pos{i}"] = row['machine_id']
    
    # Prepare API payload - note that recommend_data should be a JSON string, not an object
    payload = {
        "recommend_data": json.dumps(machine_positions)
    }
    
    # Post to API2
    api_url = "https://cash-dash-webplus.luckfun.vip/machine_recommend/update_common"
    
    try:
        response = requests.post(api_url, json=payload)
        print(f"API2 Response Status: {response.status_code}")
        print(f"API2 Response: {response.text}")
        print(f"Sent machine positions: {machine_positions}")
        return response
    except Exception as e:
        print(f"Error posting to API2: {e}")
        return None

def preview_data(csv_file='example2.csv'):
    """
    Preview the data that will be sent to API2
    
    Args:
        csv_file (str): Path to the CSV file containing machine data
    """
    # Read data from CSV
    df = pd.read_csv(csv_file)
    
    # Sort by machine_value_rank (ascending order, rank 1 is best)
    df_sorted = df.sort_values('machine_value_rank')
    
    # Take top 10 machines
    top_10_machines = df_sorted.head(10)
    
    print("Top 10 machines by rank:")
    print(top_10_machines[['machine_id', 'machine_value_rank', 'machine_value']].to_string(index=False))
    
    # Create the machine position mapping
    machine_positions = {}
    for i, (_, row) in enumerate(top_10_machines.iterrows(), 1):
        machine_positions[f"machine_pos{i}"] = row['machine_id']
    
    print(f"\nMachine positions that will be sent to API2:")
    for pos, machine in machine_positions.items():
        print(f"{pos}: {machine}")
    
    return machine_positions

if __name__ == "__main__":
    print("=== Preview Data ===")
    preview_data()
    
    print("\n=== Sending to API2 ===")
    post_data_to_api2()
