import pandas as pd
import requests
import json
import logging
import os
from datetime import datetime
from prestodb import dbapi
from CF_user_SVD_class import generate_recommendations

def setup_logging(log_file='user_recommendation.log'):
    """
    Setup logging configuration to write to both console and file
    """
    # Create logs directory if it doesn't exist
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # Full path to log file
    log_path = os.path.join(log_dir, log_file)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, mode='a', encoding='utf-8'),  # Append to file
            logging.StreamHandler()  # Also output to console
        ]
    )

    # Get logger
    logger = logging.getLogger(__name__)

    # Log session start
    logger.info("="*60)
    logger.info(f"🚀 User Recommendation System - Session Started")
    logger.info(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📁 Log file: {log_path}")
    logger.info("="*60)

    return logger

def fetch_last_day_features_df(s_sql):
    """
    Execute SQL query and return DataFrame
    """
    conn = dbapi.connect(
        host='*********',
        port=8082,
        user='ta',
        catalog='hive',
        schema='ws_default_dev',
        http_scheme='http',
    )
    cur = conn.cursor()
    sql_str = s_sql

    cur.execute(sql_str)
    rows = cur.fetchall()
    columns = [desc[0] for desc in cur.description]
    conn.close()
    return pd.DataFrame(rows, columns=columns)

# SQL queries
sql_machine_value = '''
SELECT "machine_id", "distinct_user_num", "avg_ggr", "popularity_score", "machine_value", "machine_value_rank", "up_date" FROM "hive"."ws_default_product"."lucky_legends_machine_value"

'''

sql_user_machine_selected_bundle = '''
SELECT "account_id", "bet_amount_totals", "bundle_id", "up_date" FROM "hive"."ws_default_product"."lucky_legends_user_machine"
'''

def post_data_to_api():
    """
    Generate personalized recommendations using CF and post to API1
    """
    logger = logging.getLogger(__name__)
    logger.info("🔄 Generating personalized recommendations using CF...")

    try:
        # Generate recommendations using CF_user_SVD_class
        df = generate_recommendations(output_file=None)

        logger.info(f"✅ Generated recommendations for {len(df)} users")

        # Convert DataFrame to the required API format
        recommend_data = []
        for _, row in df.iterrows():
            record = {
                "account_id": int(row['account_id']),
                "bundle_id": row['bundle_id'],
                "date": str(row['up_date']),
                "machine_pos1": row['machine_pos_1'],
                "machine_pos2": row['machine_pos_2'],
                "machine_pos3": row['machine_pos_3'],
                "machine_pos4": row['machine_pos_4'],
                "machine_pos5": row['machine_pos_5'],
                "machine_pos6": row['machine_pos_6'],
                "machine_pos7": row['machine_pos_7'],
                "machine_pos8": row['machine_pos_8'],
                "machine_pos9": row['machine_pos_9'],
                "machine_pos10": row['machine_pos_10']
            }
            recommend_data.append(record)

        logger.info(f"📊 Prepared {len(recommend_data)} recommendation records for API1")

        # Prepare API payload
        payload = {
            "recommend_data": json.dumps(recommend_data)
        }

        # Post to API
        api_url = "https://cash-dash-webplus.luckfun.vip/machine_recommend/update_personalization"
        logger.info(f"🌐 Posting to API1: {api_url}")

        response = requests.post(api_url, json=payload)
        logger.info(f"✅ API1 Response Status: {response.status_code}")
        logger.info(f"📝 API1 Response: {response.text}")
        return response

    except Exception as e:
        logger.error(f"❌ Error posting to API1: {e}")
        logger.exception("Full error traceback:")
        return None

def post_data_to_api_batch():
    """
    Generate personalized recommendations using CF and post to API1 in batches
    """
    logger = logging.getLogger(__name__)
    logger.info("🔄 Generating personalized recommendations using CF...")

    try:
        # Generate recommendations using CF_user_SVD_class
        df = generate_recommendations(output_file=None)

        logger.info(f"✅ Generated recommendations for {len(df)} users")

        batch_size = 1000
        total_rows = len(df)
        total_batches = (total_rows + batch_size - 1) // batch_size

        logger.info(f"📦 Starting batch processing: {total_rows} rows in {total_batches} batches")

        successful_batches = 0
        failed_batches = 0

        for i in range(0, total_rows, batch_size):
            batch_num = i//batch_size + 1
            logger.info(f"🔄 Processing batch {batch_num}/{total_batches}")

            # Get batch data
            batch_df = df.iloc[i:i+batch_size]

            # Convert batch to API format
            recommend_data = []
            for _, row in batch_df.iterrows():
                record = {
                    "account_id": int(row['account_id']),
                    "bundle_id": row['bundle_id'],
                    "date": str(row['up_date']),
                    "machine_pos1": row['machine_pos_1'],
                    "machine_pos2": row['machine_pos_2'],
                    "machine_pos3": row['machine_pos_3'],
                    "machine_pos4": row['machine_pos_4'],
                    "machine_pos5": row['machine_pos_5'],
                    "machine_pos6": row['machine_pos_6'],
                    "machine_pos7": row['machine_pos_7'],
                    "machine_pos8": row['machine_pos_8'],
                    "machine_pos9": row['machine_pos_9'],
                    "machine_pos10": row['machine_pos_10']
                }
                recommend_data.append(record)

            # Prepare API payload
            payload = {
                "recommend_data": json.dumps(recommend_data)
            }

            # Post batch to API
            api_url = "https://cash-dash-webplus.luckfun.vip/machine_recommend/update_personalization"

            try:
                response = requests.post(api_url, json=payload)
                if response.status_code == 200:
                    logger.info(f"✅ Batch {batch_num}: Success - {len(batch_df)} rows")
                    successful_batches += 1
                else:
                    logger.error(f"❌ Batch {batch_num}: Failed - Status {response.status_code}")
                    logger.error(f"Error response: {response.text}")
                    failed_batches += 1
            except Exception as e:
                logger.error(f"❌ Error posting batch {batch_num}: {e}")
                failed_batches += 1

        logger.info(f"📊 Batch processing completed:")
        logger.info(f"   ✅ Successful batches: {successful_batches}")
        logger.info(f"   ❌ Failed batches: {failed_batches}")
        logger.info(f"   📈 Total rows processed: {total_rows}")

    except Exception as e:
        logger.error(f"❌ Critical error in batch processing: {e}")
        logger.exception("Full error traceback:")

def post_data_to_api2():
    """
    Implementation for API2: update_common endpoint
    Reads machine data from SQL query, orders by rank, and sends top 10 machines to API2
    """
    logger = logging.getLogger(__name__)
    logger.info("🔄 Fetching machine value data from database...")

    try:
        # Read data from SQL query instead of CSV
        df = fetch_last_day_features_df(sql_machine_value)

        logger.info(f"✅ Fetched {len(df)} machines from database")

        # Sort by machine_value_rank (ascending order, rank 1 is best)
        df_sorted = df.sort_values('machine_value_rank')

        # Take top 10 machines
        top_10_machines = df_sorted.head(10)

        logger.info("🏆 Top 10 machines by rank:")
        for i, (_, row) in enumerate(top_10_machines.iterrows(), 1):
            logger.info(f"   {i}. {row['machine_id']} (rank: {row['machine_value_rank']}, value: {row['machine_value']:.2f})")

        # Create the machine position mapping
        machine_positions = {}
        for i, (_, row) in enumerate(top_10_machines.iterrows(), 1):
            machine_positions[f"machine_pos{i}"] = row['machine_id']

        logger.info(f"📊 Prepared machine positions: {machine_positions}")

        # Prepare API payload - note that recommend_data should be a JSON string, not an object
        payload = {
            "recommend_data": json.dumps(machine_positions)
        }

        # Post to API2
        api_url = "https://cash-dash-webplus.luckfun.vip/machine_recommend/update_common"
        logger.info(f"🌐 Posting to API2: {api_url}")

        response = requests.post(api_url, json=payload)
        logger.info(f"✅ API2 Response Status: {response.status_code}")
        logger.info(f"📝 API2 Response: {response.text}")

        return response

    except Exception as e:
        logger.error(f"❌ Error posting to API2: {e}")
        logger.exception("Full error traceback:")
        return None

def run_full_recommendation_pipeline():
    """
    Run the complete recommendation pipeline:
    1. Generate personalized recommendations using CF
    2. Send personalized recommendations to API1
    3. Generate common recommendations from machine values
    4. Send common recommendations to API2
    """
    logger = logging.getLogger(__name__)
    logger.info("🚀 Starting Full Recommendation Pipeline")
    logger.info("=" * 60)

    start_time = datetime.now()

    try:
        # Step 1 & 2: Generate and send personalized recommendations
        logger.info("📊 Step 1: Personalized Recommendations (API1)")
        logger.info("-" * 50)
        api1_start = datetime.now()
        api1_response = post_data_to_api_batch()
        api1_duration = datetime.now() - api1_start
        logger.info(f"⏱️  API1 execution time: {api1_duration.total_seconds():.2f} seconds")

        # Step 3 & 4: Generate and send common recommendations
        logger.info("🎯 Step 2: Common Recommendations (API2)")
        logger.info("-" * 50)
        api2_start = datetime.now()
        api2_response = post_data_to_api2()
        api2_duration = datetime.now() - api2_start
        logger.info(f"⏱️  API2 execution time: {api2_duration.total_seconds():.2f} seconds")

        # Calculate total duration
        total_duration = datetime.now() - start_time

        logger.info("=" * 60)
        logger.info("✅ Full Recommendation Pipeline Completed!")
        logger.info(f"⏱️  Total execution time: {total_duration.total_seconds():.2f} seconds")

        # Summary
        api1_success = api1_response and hasattr(api1_response, 'status_code')
        api2_success = api2_response and api2_response.status_code == 200

        if api1_success:
            logger.info("✅ API1 (Personalized): Success")
        else:
            logger.error("❌ API1 (Personalized): Failed")

        if api2_success:
            logger.info("✅ API2 (Common): Success")
        else:
            logger.error("❌ API2 (Common): Failed")

        # Log final status
        if api1_success and api2_success:
            logger.info("🎉 Pipeline completed successfully!")
        else:
            logger.warning("⚠️  Pipeline completed with some failures")

        return api1_response, api2_response

    except Exception as e:
        logger.error(f"❌ Pipeline failed: {e}")
        logger.exception("Full error traceback:")
        return None, None

if __name__ == "__main__":
    # Setup logging
    logger = setup_logging()

    try:
        # Run the full pipeline
        run_full_recommendation_pipeline()

        # Log session end
        logger.info("="*60)
        logger.info("🏁 Session completed successfully")
        logger.info("="*60)

    except Exception as e:
        logger.error(f"❌ Session failed: {e}")
        logger.exception("Full error traceback:")

    # Alternatively, you can run individual functions:
    # post_data_to_api_batch()  # For API1 only
    # post_data_to_api2()       # For API2 only
