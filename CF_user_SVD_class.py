# -*- coding: utf-8 -*-
"""
Created on Wed Jul 30 17:32:41 2025

@author: <PERSON><PERSON><PERSON><PERSON>
"""

import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from scipy.sparse import csc_matrix
import json
import re
import time
from prestodb import dbapi


def fetch_last_day_features_df(s_sql):
    """
    Execute SQL query and return DataFrame
    """
    conn = dbapi.connect(
        host='*********',
        port=8082,
        user='ta',
        catalog='hive',
        schema='ws_default_dev',
        http_scheme='http',
    )
    cur = conn.cursor()
    sql_str = s_sql

    cur.execute(sql_str)
    rows = cur.fetchall()
    columns = [desc[0] for desc in cur.description]
    conn.close()
    return pd.DataFrame(rows, columns=columns)


# SQL queries
sql_machine_value = '''
SELECT "machine_id", "distinct_user_num", "avg_ggr", "popularity_score", "machine_value", "machine_value_rank", "up_date" FROM "hive"."ws_default_product"."lucky_legends_machine_value"

'''

sql_user_machine_selected_bundle = '''
SELECT "account_id", "bet_amount_totals", "bundle_id", "up_date" FROM "hive"."ws_default_product"."lucky_legends_user_machine"
'''


def fix_non_standard_json(raw_str):
    # 如果已经是字典对象，直接返回
    if isinstance(raw_str, dict):
        return raw_str

    # 如果是字符串，进行JSON修复
    if isinstance(raw_str, str):
        # 处理键名无引号：{itemA → {"itemA"
        fixed = re.sub(r'([{,]\s*)([a-zA-Z_]\w*)(\s*=)', r'\1"\2":', raw_str)
        # 处理结尾多余逗号（如{itemA=100,} → {"itemA":100}）
        fixed = re.sub(r',\s*}', '}', fixed)
        return fixed

    # 其他情况返回空字典
    return {}


def normalize_items(df):
    """
    标准化数值列：每个单元格值 = 原始值 / 行总和
    
    参数:
    df -- 原始DataFrame，包含'account_id'和多个数值列
    
    返回:
    标准化后的DataFrame
    """
    # 1. 复制原始DataFrame避免修改原始数据
    normalized_df = df.copy()
    
    # 2. 识别数值列（除'account_id'外的所有列）
    item_columns = [col for col in df.columns if col != 'account_id']
    
    # 3. 计算每行的总和（忽略NaN值）
    row_sums = normalized_df[item_columns].sum(axis=1)
    
    # 4. 处理除零情况：将总和为零的行替换为NaN（避免除以零）
    row_sums[row_sums == 0] = np.nan
    
    # 5. 对每个数值列进行标准化
    for col in item_columns:
        normalized_df[col] = normalized_df[col] / row_sums
    
    # 6. 将NaN值替换为0（原始行总和为零的情况）
    normalized_df[item_columns] = normalized_df[item_columns].fillna(0)    
    return normalized_df


def SVD_full(R, n_factors):
    U, S, VT = np.linalg.svd(R, full_matrices=False)
    U_k = U[:, :n_factors]
    S_sqrt = np.sqrt(S[:n_factors])
    R_svd = U_k * S_sqrt
    return R_svd


def sparse_top_weighted_average(neighbor_ratings, sim_weights, k_neighbors):
    """
    参数：
        neighbor_ratings：DataFrame，行=邻居用户，列=物品，值=评分（0表示缺失）
        sim_weights：一维数组，邻居用户的相似度权重（形状: (n_neighbors,)）
        k_neighbors：每列最多取k个非零评分
    返回：
        predicted_scores：一维数组，每个物品的预测评分
    """
    # 1. 转换DataFrame为CSC稀疏矩阵（按列压缩）
    sparse_mat = csc_matrix(neighbor_ratings.values)
    n_items = sparse_mat.shape[1]  # 物品总数
    predicted_scores = np.zeros(n_items)  # 初始化预测结果
    
    # 2. 遍历每一列（物品）
    for item_idx in range(n_items):
        # 提取当前列的非零评分位置和值
        col_start = sparse_mat.indptr[item_idx]
        col_end = sparse_mat.indptr[item_idx + 1]
        ratings = sparse_mat.data[col_start:col_end]  # 非零评分值
        user_indices = sparse_mat.indices[col_start:col_end]  # 对应的用户索引
        
        # 3. 若无有效评分则跳过
        if len(ratings) == 0:
            continue
            
        # 4. 提取这些用户的相似度权重
        weights = sim_weights[user_indices]
        
        # 5. 筛选Top-K个最高评分的邻居（若不足k则取全部）
        k = min(k_neighbors, len(ratings))
        ratings = ratings[0:k]
        weights = weights[0:k]
                
        # 6. 计算加权平均评分
        weighted_sum = np.dot(ratings, weights)
        weights_sum = np.sum(weights)
        predicted_scores[item_idx] = weighted_sum / weights_sum if weights_sum > 0 else 0
        
    return predicted_scores


def get_top_recommendations_split(rec_ratings, top_n):
    """
    返回分离的推荐表：物品名和概率分别存储
    :param rec_ratings: 同方案一
    :param top_n: 同方案一
    :return: (top_items_df, top_scores_df) 两个DataFrame
    """
    # 存储物品名
    top_items = rec_ratings.apply(
        lambda row: row.nlargest(top_n).index.tolist(), 
        axis=1
    )
    # 存储概率值
    top_scores = rec_ratings.apply(
        lambda row: row.nlargest(top_n).values.tolist(), 
        axis=1
    )
    
    # 转换为DataFrame
    top_items_df = pd.DataFrame(
        top_items.tolist(), 
        index=rec_ratings.index,
        columns=[f'machine_pos_{i+1}' for i in range(top_n)]
    )
    top_scores_df = pd.DataFrame(
        top_scores.tolist(), 
        index=rec_ratings.index,
        columns=[f'machine_rating_{i+1}' for i in range(top_n)]
    )
    return top_items_df, top_scores_df


def data_pre(data_user_machine,data_machine_value, machine_value_top_n):
    top_machine_list = (
            data_machine_value.sort_values('machine_value', ascending=False)
            .head(machine_value_top_n)['machine_id'].tolist()
            )
    user_bundle = pd.concat([data_user_machine['account_id'],data_user_machine['bundle_id']], axis=1)

    #应用修复函数
    data_user_machine['bet_amount_totals'] = data_user_machine['bet_amount_totals'].apply(fix_non_standard_json)

    #解析为字典（如果还不是字典的话）
    def ensure_dict(x):
        if isinstance(x, dict):
            return x
        elif isinstance(x, str):
            return json.loads(x)
        else:
            return {}

    data_user_machine['bet_amount_totals'] = data_user_machine['bet_amount_totals'].apply(ensure_dict)
    #jason标准化和列重排序
    goods_df = pd.json_normalize(data_user_machine['bet_amount_totals']).fillna(0)
    goods_df = goods_df[top_machine_list]

    #归一化
    goods_df_normalized = normalize_items(goods_df)

    #分割全为0交互数据
    mask_all_zero = (goods_df_normalized.abs()<1e-9).all(axis=1)
    R_zero = pd.concat([user_bundle[mask_all_zero],goods_df_normalized[mask_all_zero]], axis=1)
    R_zero.reset_index(drop=True, inplace=True)
    R_nonzero = pd.concat([user_bundle[~mask_all_zero],goods_df_normalized[~mask_all_zero]], axis=1)
    R_nonzero.reset_index(drop=True, inplace=True)
    return R_nonzero, R_zero


def R_zero_recommendations(R_zero, n_recommend):
    user_ids = R_zero.account_id
    bundle_id = R_zero.bundle_id
    R_zero = R_zero.drop(['account_id'],axis=1)
    R_zero = R_zero.drop(['bundle_id'],axis=1)
    item_ids = R_zero.columns[0:n_recommend]
    
    df = pd.DataFrame(
            index=user_ids,    # 行索引为用户ID
            columns=item_ids,
            #columns=[f'machine_pos_{i+1}' for i in range(top_n)],  # 列索引为物品ID
            dtype=str          # 指定元素为字符串类型
            )
    
    for user in user_ids:
        for item in item_ids:
            df.loc[user,item] = item
            
    df.columns = [f'machine_pos_{i+1}' for i in range(n_recommend)]
    df.reset_index(drop=True, inplace=True)
    df.insert(0,'account_id',user_ids)
    df.insert(1,'bundle_id',bundle_id)  
    return df


class UserBasedCFWithSVD:
    def __init__(self, n_factors=10, k_neighbors=30, n_recommend=10, min_similarity = 0.1):
        """
        初始化协同过滤推荐器
        
        参数:
        n_factors: SVD降维后的维度数
        k_neighbors: 用于推荐的相似用户数量
        n_recommend: 推荐物品数量
        """
        self.n_factors = n_factors
        self.k_neighbors = k_neighbors
        self.n_recommend = n_recommend
        self.min_similarity = min_similarity
        self.user_ids = None
        self.item_ids = None
        self.bundle_id = None
        self.user_similarity = None
        self.R_svd = None        
        
    def fit(self, R_nonzero):
        """
        训练推荐模型
        
        参数:
        R: 用户-物品评分矩阵(DataFrame)，行=用户，列=物品
        """
        # 数据预处理
        self.bundle_id = R_nonzero['bundle_id']
        R_nonzero = R_nonzero.drop(['bundle_id'],axis=1)
        R = R_nonzero.set_index('account_id')
        R_filled = R.fillna(0)  # 用0填充缺失值
        
        # 存储原始评分矩阵
        self.R = R_filled
        self.user_ids = R_filled.index
        self.item_ids = R_filled.columns
        
        # SVD降维
        self.R_svd = SVD_full(R.values, self.n_factors)
        
        # 计算用户相似度
        self.user_similarity = cosine_similarity(self.R_svd)
        np.fill_diagonal(self.user_similarity, 0)        
        
    def recommend_all_users(self):
        rec_ratings = pd.DataFrame()
        all_rows = []
        for user_id in self.user_ids:
            # 1. 选择当前用户
            user_idx = np.where(self.user_ids == user_id)[0][0]
                
            # 2. 筛选有效邻居用户
            user_sim_scores = self.user_similarity[user_idx]    
            top_similar_idxs = np.argsort(user_sim_scores)[::-1]
            valid_mask = user_sim_scores > self.min_similarity  # 过滤低相似度用户
            top_similar_idxs = [idx for idx in top_similar_idxs if valid_mask[idx]]
                
            # 3. 向量化预测评分
            user_ratings = self.R.iloc[user_idx]    
            unrated_mask = user_ratings == 0   
    
            neighbor_ratings = self.R.iloc[top_similar_idxs].loc[:,unrated_mask]  # 仅取未评分物品
            sim_weights = user_sim_scores[top_similar_idxs][:, np.newaxis]
                
            # 4. 计算加权评分
            topsim_weighted_ratings = sparse_top_weighted_average(neighbor_ratings, sim_weights, self.k_neighbors)
                
            # 5. 生成推荐概率表
            rec_items = self.item_ids[~unrated_mask].union(self.item_ids[unrated_mask],sort=False)
            rec_ratings_array = np.hstack((np.array(user_ratings[~unrated_mask]),topsim_weighted_ratings))
            rec_ratings_i = pd.DataFrame([rec_ratings_array], columns=rec_items)
            rec_ratings_i = rec_ratings_i[self.item_ids]
            #rec_list = pd.concat([rec_list, rec_list_i], ignore_index=True)
            all_rows.append(rec_ratings_i)
            
        rec_ratings = pd.concat(all_rows, ignore_index=True)
        rec_ratings = rec_ratings.set_index(self.user_ids)
        table_rec_pos, table_rec_ratings = get_top_recommendations_split(rec_ratings,self.n_recommend)
        table_rec_pos.reset_index(drop=True, inplace=True)
        table_rec_pos.insert(0,'account_id',self.user_ids)
        table_rec_pos.insert(1,'bundle_id',self.bundle_id)
        return table_rec_pos, table_rec_ratings
        
        
def generate_recommendations(machine_value_top_n=30, n_SVD=10, k_neighbors=50, n_recommend=10,
                           class_str='C', up_date_str='********', output_file='result.csv'):
    """
    Generate user recommendations using collaborative filtering with SVD

    Parameters:
    machine_value_top_n: Number of top machines to consider based on machine value
    n_SVD: Number of factors for SVD decomposition
    k_neighbors: Number of similar users to consider for recommendations
    n_recommend: Number of items to recommend per user
    class_str: Class identifier for the output
    up_date_str: Date string for the output
    output_file: Output CSV file name

    Returns:
    table_pos_total: DataFrame with recommendations for all users
    """
    start = time.perf_counter()

    print("📊 开始生成推荐...")

    # 1. 从数据库获取数据
    print("🔄 正在从数据库获取数据...")
    data_user_machine = fetch_last_day_features_df(sql_user_machine_selected_bundle)
    data_machine_value = fetch_last_day_features_df(sql_machine_value)

    print(f"✅ 获取用户机器数据: {len(data_user_machine)} 行")
    print(f"✅ 获取机器价值数据: {len(data_machine_value)} 行")

    # 2. 数据预处理
    print("🔄 正在进行数据预处理...")
    R_nonzero, R_zero = data_pre(data_user_machine, data_machine_value, machine_value_top_n)

    print(f"✅ 非零交互用户: {len(R_nonzero)} 个")
    print(f"✅ 零交互用户: {len(R_zero)} 个")

    # 3. 计算R_nonzero的推荐表
    print("🔄 正在为有交互用户生成推荐...")
    recommender = UserBasedCFWithSVD(n_SVD, k_neighbors, n_recommend)
    recommender.fit(R_nonzero)
    table_rec_pos_nonzero, table_rec_ratings = recommender.recommend_all_users()

    # 4. 计算R_zero的推荐表
    print("🔄 正在为无交互用户生成推荐...")
    table_rec_pos_zero = R_zero_recommendations(R_zero, n_recommend)

    # 5. 合并处理，生成总推荐表
    print("🔄 正在合并推荐结果...")
    table_pos_total = pd.concat([table_rec_pos_nonzero, table_rec_pos_zero], axis=0, ignore_index=True)
    table_pos_total.insert(2, 'class', class_str)
    table_pos_total.insert(3, 'up_date', up_date_str)
    table_pos_total = table_pos_total.sort_values(by='bundle_id')

    # 6. 保存结果
    if output_file:
        table_pos_total.to_csv(output_file, index=False)
        print(f"💾 推荐结果已保存到: {output_file}")

    end = time.perf_counter()
    print(f"⏳ 总运行时长: {end - start:.4f} 秒")
    print(f"✅ 生成推荐完成! 总用户数: {len(table_pos_total)}")

    return table_pos_total


if __name__ == "__main__":
    # 使用默认参数生成推荐
    result = generate_recommendations()

        
        
        

